import User from "../model/userModel.js"; //from the model
import bcrypt from "bcrypt"
import jwt from "jsonwebtoken";

//user register API
export const userRegistration = async (req, res) => {
    try {
        const { username, email, password, role } = req.body;  //postman  ....frontend
        console.log("username", username)
        console.log("email", email)
        //status code 
        if (!username || !email || !password) {
            return res.status(400).json({ message: "Username, email, password must required" });
        }

        const existingUser = await User.findOne({ email });

        if (existingUser) {
            return res.status(409).json({ message: "<PERSON><PERSON> is already registered!" })
        }

        const hashedPassword = await bcrypt.hash(password, 14); // password hashing
        const newUser = await User.create({
            username,
            email,
            password: hashedPassword,
            role
        })
        res.status(201).json({ message: "User registered successfully", data: newUser })

    } catch (err) {
        res.status(500).json({ error: "Internal server error" })

    }
}


//user login api
export const userLogin = async (req, res) => {
    try {
        const { email, password } = req.body;  //postman  ....frontend

        //status code 
        if (!email || !password) {
            return res.status(400).json({ message: "email, password must required" });
        }

        const existingUser = await User.findOne({ email });

        if (!existingUser) {
            return res.status(401).json({ message: "Invalid email or password" })
        }

        const ismatch = await bcrypt.compare(password, existingUser.password);

        if (!ismatch) {
            return res.status(401).json({ message: "Invalid email or password" })
        }

        const payload = { id: existingUser.id, role: existingUser.role }
        const token = jwt.sign(payload, process.env.JWT_SECRETE, { expiresIn: "1h" });

        res.status(200).json({ message: "User login successfull", token, data: existingUser })

    } catch (err) {
        res.status(500).json({ error: "Internal server error" })

    }
}

// get all the users
export const getAllUsers = async (req, res) => {
    try {
        const user = await User.find();
        if(!user){
            res.status(400).json({ message: "User not found"});
        }
        res.status(200).json({ message: "Successfully get all the users", data: user });
    } catch (err) {
        res.status(500).json({ error: "Internal server error" });
    }
}


//fetch single users
export const singleUser = async (req, res) => {
    try {
        const { id } = req.params;
        console.log("Id from the postman : ", id)
        const users = await User.findById(id);
        if(!users){
            return res.status(400).json({ message: "User not found" });
        }
        res.status(200).json({ message: "Single user fetch successfully", data:users})
    } catch (err) {
        res.status(500).json({ error: "Internal server error" });
    }
}


//user profile
export const userProfile= async(req, res)=>{
    try{
        const id=req.user.id;
        const user= await User.findById(id);
        if(!user){
            return res.status(400).json({message: " User not found"})
        }
         res.status(200).json({ message: "User profile fetch successfully", data:user})
    }catch(err){
        res.status(500).json({ error: "Internal server error" });
    }
}


//update  userProfile
export const updateUser = async (req, res) => {
    try {
        const { id } = req.params;
        const {username, password } = req.body;  //postman  ....frontend
        //status code 
        if (!username || !password) {
            return res.status(400).json({ message: "Username and password must required" });
        }
        const existingUser = await User.findOne({ username });
        const user = await User.findByIdAndUpdate(id, req.body, { new: true });

        res.status(200).json({ message: "User updated successfully", data: user })

    } catch (err) {
        res.status(500).json({ error: "Internal server error" });

    }
}


//delete user
export const deleteUser = async (req, res) => {
    try {
        const { id } = req.params;
        const user = await User.findByIdAndDelete(id);
         if(!user){
            return res.status(400).json({ message: "User not found" });
        }
        res.status(200).json({ message: "User deleted successfully"})
    } catch (err) {
        res.status(500).json({ error: "Internal server error" });

    }
}


