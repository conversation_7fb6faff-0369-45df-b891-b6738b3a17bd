import { Router } from "express";
import { createTodo, deleteTodo, getTodo, singleTodo, updateTodo } from "../controller/toDoController.js";
import { isAuthenticated, restrictTo, Role } from "../middleware/authMiddleware.js";

const router=Router();


// RESTful todo routes
router.route("/").post(isAuthenticated, restrictTo(Role.User), createTodo);  // POST /api/v1/todo
router.route("/").get(getTodo);                                              // GET /api/v1/todo
router.route("/:id").get(isAuthenticated, singleTodo);                       // GET /api/v1/todo/:id
router.route("/:id").patch(isAuthenticated, updateTodo);                     // PATCH /api/v1/todo/:id
router.route("/:id").delete(isAuthenticated, deleteTodo);                    // DELETE /api/v1/todo/:id


export default router
