import express from "express"
import connectDB from "./config/mongodb.js"; //import from the mongodb.js 

import userRoutes from "./routes/userRoutes.js" //import from the userRoutes
import toDoRoutes from "./routes/toDoRoutes.js" 

import dotenv from "dotenv"
dotenv.config();


const app=express();
const PORT= process.env.PORT || 3000;

// Add request logging middleware
app.use((req, res, next) => {
    console.log(`${req.method} ${req.url} - Content-Type: ${req.headers['content-type']}`);
    next();
});

// JSON parsing with error handling
app.use(express.json({
    limit: '10mb',
    verify: (req, res, buf, encoding) => {
        // Log the raw body for debugging
        if (buf && buf.length) {
            console.log('Raw body:', buf.toString('utf8').substring(0, 200));
        }
    }
}));

// JSON parsing error handler
app.use((error, req, res, next) => {
    if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
        console.error('JSON Parse Error:', error.message);
        console.error('Request URL:', req.url);
        console.error('Request Method:', req.method);
        console.error('Content-Type:', req.headers['content-type']);
        return res.status(400).json({
            error: 'Invalid JSON format',
            message: 'Please check your request body format'
        });
    }
    next();
});

connectDB()//call the function from the mongodb.js

//Routes

// app.use("/register", (req, res)=>{
//     res.send("Register is test")
// })

app.use("/api", userRoutes)
app.use("/api/todo", toDoRoutes)


app.listen(PORT, ()=>{
    console.log(`Server is running on  the port ${PORT}`)   
})





// npm i nodemon